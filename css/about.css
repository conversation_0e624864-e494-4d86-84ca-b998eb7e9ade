/* Google fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Common css -start
=========================== */
html {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

body {
  font-family: "Poppins", sans-serif;
  padding: 0;
  margin: 0;
}

* {
  padding: 0;
  margin: 0;
  outline: 0px;
  scroll-behavior: smooth;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}
/* preloader- start 
===============================*/

#preloader {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: fixed;
  overflow: visible;
  background: #fff url("../images/preloader.gif") no-repeat center center;
  animation: loader 5s infinite ease;
}

/* preloader - end 
=================================*/
/* navbar - css 
======================== */
.nav-section {
  background: #333646;
  padding: 10px;
}
.navbar {
  width: 100%;
}
label.logo {
  color: white;
  font-size: 35px;
  line-height: 80px;
  padding: 0 100px;
  font-weight: bold;
}

nav ul li {
  display: inline-block;
  line-height: 80px;
  margin: 0 10px;
}
nav ul li a {
  color: white;
  font-size: 17px;
  padding: 7px 13px;
  border-radius: 3px;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 00.2px;
}
a.active,
a:hover {
  color: #fcb532;
  transition: 0.5s;
}
.checkbtn {
  font-size: 30px;
  color: white;
  float: right;
  line-height: 80px;
  margin-right: 40px;
  cursor: pointer;
  display: none;
}
#check {
  display: none;
}

.drop-down-list {
    visibility: hidden;
    display: flex;
    flex-direction: column;
    color: #fff;
    position: absolute;
    left: 72%;
    z-index: 10000;
    background-color: #333646;
    padding: 10px 25px 10px 10px;
}

#page-dropdown:hover .drop-down-list {
  visibility: visible;
}
/* End Nav Style */

/* Hero Box Start*/
.about-hero-box {
  height: 400px;
  background-color: #0a0919;
}

.hero-nav {
  display: flex;
  justify-content: center;
  /* align-items: center; */
  text-align: start;
  flex-direction: column;
  height: 400px;
}
.hero-nav h1 {
  font-size: 60px;
  font-weight: 700;
  color: #fcb532;
}
.hero-nav span {
  color: #ffff;
}
.hero-nav ul {
  height: 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
}
.hero-nav ul li {
  list-style: none;
  margin: 10px;
  color: #ffff;
}
.hero-nav ul li a {
  text-decoration: none;
  color: #ffff;
}
.hero-nav ul li a:hover {
  color: #fcb532;
}
.about-hero-img {
  height: 400px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.about-hero-img img {
  height: 300px;
}
/* Hero Box End*/

/* About info start */
.about-section {
  height: 900px;
  background-color: #252734;
  text-align: center;
  overflow: hidden;
}

.about-img {
  height: 900px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-details {
  height: 900px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: left;
}
.about-details h3 {
  color: #fcb532;
  font-size: 18pt;
  font-weight: 500;
}
.about-details h1 {
  color: #ffff;
  font-size: 36pt;
  font-weight: 700;
}
.about-details p {
  color: #ffff;
  font-size: 16px;
  font-weight: 400;
  font-family: "Open Sans", sans-serif;
}
.about-contact-info {
  display: flex;
  margin-top: 60px;
}
.about-contact-info img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}
.about-contact-info h3 {
  font-size: 18pt;
  color: #ffff;
}

.about-contact-info .fab {
  font-size: 20px;
  color: #ffff;
  padding: 10px;
  border: 1px solid #ffff;
  border-radius: 50%;
  transition: 0.2s ease-in-out;
  margin-right: 10px;
}
.about-contact-info .fa-facebook-f {
  padding: 10px 15px;
}
.about-contact-info .fab:hover {
  color: #fcb532;
  border: 1px solid #fcb532;
}
/* About info end */

/* Testimonial Start */
.testimonial-section {
  background-color: #333646;
  overflow: hidden;
  height: 500px;
}
.testimonial-info {
  height: 500px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: #ffff;
}
.testimonial-info h3 {
  font-size: 30pt;
  font-weight: 700;
  color: #fcb532;
  margin-bottom: 30px;
}
.testimonial-section img {
  width: 75%;
}

/* Testimonial End */

/* Instructor Start */
.instructor-section {
  padding: 100px 0;
  background-color: #252734;
  text-align: center;
}
.instructor-section p {
  color: #fcb532;
  font-size: 16pt;
  font-weight: 500;
  margin-bottom: 0;
}
.instructor-section h3 {
  font-size: 30pt;
  color: #ffff;
  font-weight: 700;
  margin-bottom: 60px;
}
.instructor-card {
  position: relative;
}
.instructor-overlay {
  display: flex;
  justify-content: center;
  opacity: 0;
  height: 0;
  transition: all 0.3s ease;
}
.instructor-info {
  background-color: #fcb532;
  border-radius: 5px;
  color: #ffff;
  padding: 15px 10px;
  width: 80%;
  margin: 0 auto;
  position: absolute;
  bottom: -40px;
}
.instructor-info h3 {
  font-size: 24px;
  margin-bottom: 0;
}
.instructor-info p {
  color: #ffff;
  font-size: 16px;
}
.instructor-link {
  position: absolute;
  bottom: 20%;
}
.instructor-link .fab {
  color: #ffff;
  font-size: 20px;
  padding: 10px;
  border: 1px solid #fcb532;
  border-radius: 50%;
  background-color: #ffff;
  color: #fcb532;
  transition: border-radius 0.3s ease-in 0s;
}
.instructor-link .fa-facebook-f {
  padding: 10px 15px;
  background-color: #ffff;
  color: #fcb532;
  border: 1px solid #fcb532;
}
.instructor-link .fab:hover {
  border: 1px solid #fcb532;
  color: #fff;
  background-color: #fcb532;
}
.instructor-card:hover .instructor-overlay {
  opacity: 1;
  height: 100%;
}
/* Instructor End */

/* footer */
/* /* Footer Style Start */
.footer-section {
  height: 350px;
}
.footer-nav-section {
  height: 300px;
  background-color: #333646;
}
.footer-nav-container {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.footer-nav-container ul {
  display: flex;
}
.footer-nav-container ul li {
  list-style: none;
  margin: 30px 20px;
}
.footer-nav-container ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.footer-nav-container ul li a:hover {
  color: #fcb532;
  transition: 00.2s ease-in;
}
.footer-social-icon {
  margin: 0 0 30px;
}
.footer-social-icon i {
  color: #fff;
  font-size: 30px;
  margin: 0 30px;
}
.footer-social-icon i:hover {
  color: #fcb532;
  transition: 0.3s ease;
}
.footer-nav-card {
  position: relative;
  cursor: pointer;
}
.footer-nav-card img {
  width: 100%;
}
.footer-img-overlay {
  background-color: #fcb532;
  color: #ffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  transition: all 0.5s ease-in;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transition: all 0.3s ease;
  width: 0;
  text-align: center;
}
.footer-img-overlay h5 {
  margin-top: 20px;
  font-size: 16px;
}
.footer-img-overlay .fab {
  font-size: 26px;
}
.footer-nav-card:hover .footer-img-overlay {
  opacity: 1;
  width: 100%;
  position: absolute;
}
.footer {
  height: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}
/* /* Footer Style End  */
@media screen and (max-width: 991px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: ease 0.2s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  .navbar a:hover,
  .navbar a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }
  .checkbtn {
    display: block;
  }

  .drop-down-list {
    height: auto;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .hero-section {
    height: auto;
    padding: 50px 10px;
    overflow: hidden;
    background-image: url(../images/heroBg.jpg);
    /* background-position-x: -750px; */
  }
  .hero-info h1 {
    font-size: 65px;
  }
  .hero-info p {
    font-size: 16pt;
    margin: 30px 0;
    padding: 0;
  }
  .footer-section {
    height: auto;
  }
  .footer-nav-container {
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 40px 0;
  }
  .testimonial-section {
    height: auto;
    padding: 40px 0;
  }
  .testimonial-info {
    height: auto;
    margin-top: 50px;
  }
  .testimonial-section {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .instructor-section {
    padding: 50px 0;
  }
  .instructor-info {
    background-color: #fcb532;
    border-radius: 5px;
    color: #ffff;
    padding: 15px 10px;
    width: 80%;
    margin: 0 auto;
    position: absolute;
    bottom: 0;
  }
  .instructor-link {
    position: absolute;
    bottom: 25%;
  }
  .footer-nav-section {
    height: auto;
  }
}
@media screen and (max-width: 768px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .hero-nav {
    height: auto;
    padding: 30px 0;
  }
  .about-hero-box {
    height: auto;
  }
  .about-section {
    height: auto;
    overflow: hidden;
  }
  .about-hero-img {
    display: none;
  }
  .about-img {
    height: auto;
    padding: 50px 0 30px;
  }
  .about-details {
    height: auto;
    padding: 30px 0;
  }
  .testimonial-section {
    height: auto;
    padding: 50px 0;
  }
  .testimonial-info {
    height: auto;
  }
  .testimonial-section-info img {
    padding: 0 40px;
  }
  .instructor-section {
    height: auto;
    padding: 40px 0;
    overflow: hidden;
  }
  .instructor-card {
    height: auto;
    position: relative;
  }
  .footer-section {
    height: auto;
    overflow: hidden;
  }
  .footer-nav-container {
    height: auto;
  }
  .footer-nav-section {
    height: auto;
    padding: 30px 0;
  }
  .instructor-info {
    position: absolute;
    bottom: 0;
    width: 82%;
  }
  .instructor-link {
    position: absolute;
    bottom: 30%;
  }
  .footer-section {
    height: auto;
  }
  .footer-social-icon i {
    color: #fff;
    font-size: 24px;
    margin: 20px 10px 0;
  }
  .footer-nav-container ul {
    display: none;
  }
  .footer {
    height: auto;
  }

  .checkbtn {
    display: block;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .footer-section {
    height: auto;
  }
}
@media screen and (max-width: 526px) {
  .hero-nav h1 {
    font-size: 40px;
    font-weight: 700;
    color: #fcb532;
  }
  .about-details {
    text-align: center;
    padding: 0 0 30px;
  }
  .about-details h1 {
    font-size: 26pt;
  }
  .about-details p {
    color: #ffff;
    font-size: 16px;
    text-align: justify;
    margin-bottom: 0;
  }
  .about-contact-info {
    display: flex;
    flex-direction: column;
    margin-top: 30px;
  }
  .about-contact-info {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .about-contact-info img {
    margin-bottom: 30px;
  }
  .about-contact-info p {
    margin-bottom: 20px;
  }
  .testimonial-info {
    text-align: center;
  }
  .testimonial-info h3 {
    font-size: 30px;
    text-align: center;
  }
  .testimonial-info p {
    text-align: justify;
  }
  .instructor-section h3 {
    font-size: 30px;
    text-align: center;
  }
  .footer-img-overlay {
    text-align: center;
    font-size: 20px;
  }
  .footer-img-overlay .fab {
    font-size: 25px;
  }
  .footer-img-overlay h5 {
    font-size: 12px;
  }
  .footer p {
    text-align: center;
    font-size: 16px;
  }
  .footer-nav-container {
    padding: 0;
  }
}
