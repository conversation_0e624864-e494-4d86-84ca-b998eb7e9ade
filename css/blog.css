/* Google fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Common css -start
=========================== */
html {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}
body{
  font-family: "Poppins", sans-serif;
  padding: 0;
  margin: 0;
}

*{
  padding: 0;
  margin: 0;
  outline: 0px;
  scroll-behavior: smooth;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}


body {
  font-family: "Poppins", sans-serif;
  padding: 0;
  margin: 0;
}
.global-color {
  color: #fcb532;
}

/* preloader- start 
===============================*/

#preloader {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: fixed;
  overflow: visible;
  background: #fff url("../images/preloader.gif") no-repeat center center;
  animation: loader 5s infinite ease;
}

/* preloader - end 
=================================*/
/* Nav style */
* {
  padding: 0;
  margin: 0;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}
.nav-section {
  background: #333646;
  padding: 10px;
}
.navbar {
  width: 100%;
}
label.logo {
  color: white;
  font-size: 35px;
  line-height: 80px;
  padding: 0 100px;
  font-weight: bold;
}

nav ul li {
  display: inline-block;
  line-height: 80px;
  margin: 0 10px;
}
nav ul li a {
  color: white;
  font-size: 17px;
  padding: 7px 13px;
  border-radius: 3px;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 00.2px;
}
.navbar a.active,
.navbar a:hover {
  color: #fcb532;
  transition: 0.5s;
}
.checkbtn {
  font-size: 30px;
  color: white;
  float: right;
  line-height: 80px;
  margin-right: 40px;
  cursor: pointer;
  display: none;
}
#check {
  display: none;
}

.drop-down-list {
  visibility: hidden;
  display: flex;
  flex-direction: column;
  color: #fff;
  position: absolute;
  left: 70%;
  z-index: 10000;
  background-color: #333646;
  padding: 10px 100px 10px 30px;
}

#page-dropdown:hover .drop-down-list {
  visibility: visible;
}
/* end nav style */

/* Hero Box Start*/
.blog-hero-box {
  height: 400px;
  background-color: #0a0919;
}
.hero-nav {
  display: flex;
  justify-content: center;
  /* align-items: center; */
  text-align: start;
  flex-direction: column;
  height: 400px;
}
.hero-nav h1 {
  font-size: 60px;
  font-weight: 700;
  color: #fcb532;
}
.hero-nav span {
  color: #ffff;
}
.hero-nav ul {
  height: 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
}
.hero-nav ul li {
  list-style: none;
  margin: 10px;
  color: #ffff;
}
.hero-nav ul li a {
  text-decoration: none;
  color: #ffff;
}
.hero-nav ul li a:hover {
  color: #fcb532;
}
.blog-hero-img {
  height: 400px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.blog-hero-img img {
  height: 300px;
}

/* blog section style */
.blog-section {
  background-color: #252734;
  padding: 100px 0 0;
}
.blog-container {
  width: 100%;
}

.blog-container ul {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  margin: 20px 0 0 0;
  padding: 0;
}
.blog-container ul li {
  list-style: none;
  color: #fcb532;
}
.blog-container ul li p {
  font-size: 16pt;
  padding: 0 40px;
  font-family: "Open Sans", sans-serif;
  letter-spacing: 0.5px;
  margin: 0;
}
.blog-details {
  width: 80%;
  margin: 0 auto;
}
.blog-details h3 {
  color: #ffff;
  font-size: 24pt;
  font-weight: 500;
  text-align: center;
  margin-bottom: 30px;
}
.blog-details p {
  text-align: justify;
  width: 80%;
  margin: 0 auto 30px;
}
.blog-firs-para {
  color: #ffff;
  font-size: 16px;
  font-weight: 400;
  text-align: justify;
  width: 80%;
}
.blog-firs-para::first-letter {
  color: #fcb532;
  font-size: 50pt;
  font-weight: 700;
  padding-right: 8px;
  float: left;
}

.blog-section-social-icon {
  display: flex;
  color: #ffff;
  margin-top: 80px;
  margin-bottom: 50px;
  justify-content: center;
}
.blog-section-social-icon .fab {
  font-size: 30px;
  color: #fcb532;
  padding-right: 10px;
}
.blog-section-social-icon p {
  font-size: 16pt;
  font-family: "Open Sans", sans-serif;
  margin: 0 40px;
  display: flex;
}
.blog-container hr {
  border-bottom: 3px solid #ffff;
  width: 80%;
  margin: 0 auto;
}

/* news */

.news-container {
  margin-top: 60px;
}
.card {
  background-color: #333646 !important;
}
.card h4 {
  font-size: 20px;
  font-weight: 400;
  color: #fff;
}
.card p {
  color: #fff;
  margin-top: 20px;
  font-weight: 200;
  font-size: 15px;
}
.card .read-btn a{
  font-size: 16px;
  color: #fcb532;
  background-color: #333646;
  border: none;
  text-decoration: none;
  padding: 0px;
}
.card-likes {
  display: flex;
}
.card-likes p {
  margin-right: 40px;
  margin-left: 20px;
  margin-top: 0;
}

/* Social Media style */
.social-media-info {
  height: 450px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.social-media-info h5 {
  font-size: 16pt;
  font-weight: 500;
  color: #fcb532;
}
.social-media-info h3 {
  margin-top: 10px;
  font-size: 36pt;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
}
.social-media-info p {
  color: #fff;
  font-size: 16px;
}
.social-media-card {
  height: 60px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
}
.social-media-card img {
  height: 60px;
}
.social-media-follower {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 450px;
}
.follower-info {
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.follower-info h3 {
  font-size: 14pt;
  font-weight: 700;
  margin-bottom: 0;
}
.follower-info p {
  margin-bottom: 0;
  color: #949494;
}
.plus1 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 0.2px solid rgba(128, 128, 128, 0.301);
  color: #4b5adb;
  padding: 0 20px;
}
/* /* Social Media Style End  */

/* /* Footer Style Start */
.footer-section {
  height: 550px;
}
.footer-nav-section {
  height: 600px;
  background-color: #333646;
}
.footer-nav-container {
  height: 600px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.footer-nav-container ul {
  display: flex;
}
.footer-nav-container ul li {
  list-style: none;
  margin: 30px 20px;
}
.footer-nav-container ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.footer-nav-container ul li a:hover {
  color: #fcb532;
  transition: 00.2s ease-in;
}
.footer-social-icon {
  margin: 0 0 30px;
}
.footer-social-icon i {
  color: #fff;
  font-size: 30px;
  margin: 0 30px;
}
.footer-social-icon i:hover {
  color: #fcb532;
  transition: 0.3s ease;
}
.footer-nav-card {
  position: relative;
  cursor: pointer;
}
.footer-nav-card img {
  width: 100%;
}
.footer-img-overlay {
  background-color: #fcb532;
  color: #ffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  transition: all 0.5s ease-in;
  position: absolute;
  top: 0;
  bottom: 0;
  transition: all 0.3s ease;
  width: 0;
  text-align: center;
}
.footer-img-overlay h5 {
  margin-top: 20px;
  font-size: 16pt;
}
.footer-img-overlay .fab {
  font-size: 26px;
}
.footer-nav-card:hover .footer-img-overlay {
  opacity: 1;
  width: 100%;
}
.footer {
  height: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}
/* /* Footer Style End  */

@media screen and (max-width: 991px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: ease 0.2s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  .navbar a:hover,
  .navbar a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }
  .checkbtn {
    display: block;
  }

  .drop-down-list {
    height: auto;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .blog-details {
    margin-top: 60px;
  }
  .follower-info {
    padding: 5px;
  }
  .follower-info h3 {
    font-size: 16px;
  }
  .follower-info p {
    font-size: 12px;
  }
  .plus1 {
    width: 20%;
  }
}

@media screen and (max-width: 768px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .checkbtn {
    display: block;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  .navbar a:hover,
  .navbar a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .portfolio-hero-box {
    height: auto;
    padding: 30px 0;
    width: 100%;
  }
  .hero-nav {
    height: auto;
    overflow: hidden;
    width: 100%;
  }
  .blog-hero-box {
    height: auto;
    padding: 30px 0;
  }
  .blog-hero-img {
    display: none;
  }
  .blog-container ul li p {
    font-size: 16px;
    padding: 0 10px;
    font-family: "Open Sans", sans-serif;
    letter-spacing: 0.5px;
    margin: 0;
  }
  .blog-section-social-icon p {
    font-size: 16px;
    font-family: "Open Sans", sans-serif;
    margin: 0 10px;
    display: flex;
    height: 30px;
    justify-content: center;
    align-items: center;
  }
  .blog-section {
    height: auto;
  }
  .news-container {
    height: auto;
    width: 100%;
  }
  .social-media-container {
    padding: 40px 0;
  }
  .social-media-info {
    height: auto;
  }

  .social-media-info {
    height: auto;
  }
  .social-media-follower {
    height: 100%;
  }
  .social-media-card {
    width: 100%;
  }
  .plus1 {
    width: 100%;
    padding: 0;
  }
  .follower-info {
    padding: 5px;
  }
  .follower-info h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0;
    text-align: left;
  }
  .follower-info p {
    font-size: 10px;
  }
  .plus1 {
    width: 20%;
  }
  .blog-details p {
    text-align: justify;
    width: 100%;
    margin: 0 auto 30px;
  }
  .footer-social-icon {
    margin: 30px 0 30px;
  }
  .footer-section {
    height: auto;
    overflow: hidden;
  }
  .footer-nav-container {
    height: auto;
    padding: 30px 0;
  }

  .footer-nav-container ul {
    display: none;
  }
  .footer-nav-img {
    height: auto;
  }
  .footer {
    height: auto;
    overflow: hidden;
  }
  .blog-section-social-icon .fab {
    font-size: 20px;
    color: #fcb532;
    padding-right: 10px;
  }
  .footer-nav-section {
    height: auto;
  }
  .footer-img-overlay {
    background-color: #fcb532;
    color: #ffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    opacity: 0;
    transition: all 0.5s ease-in;
    position: absolute;
    top: 0;
    bottom: 30px;
    transition: all 0.3s ease;
    width: 0;
    height: 100%;
  }
}

@media (max-width: 500px) {
  .checkbtn {
    display: block;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .hero-nav h1 {
    font-size: 40px;
  }
  .blog-section {
    height: auto;
    padding: 30px;
  }
  .plus1 {
    width: 20%;
  }
  .social-media-info {
    text-align: center;
  }
  .social-media-info h3 {
    font-size: 26pt;
  }
  .social-media-info h3 {
    font-size: 30px;
  }
  .footer-social-icon .fab {
    font-size: 20px;
    margin: 10px;
  }
  .footer-img-overlay {
    text-align: center;
  }
  .footer-img-overlay .fab {
    font-size: 26px;
  }
  .footer-img-overlay h5 {
    font-size: 16px;
  }

  .footer p {
    text-align: center;
    font-size: 16px;
  }
}
