/* Google fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Common css -start
=========================== */
html {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}
body{
  font-family: "Poppins", sans-serif;
  padding: 0;
  margin: 0;
}
*{
  padding: 0;
  margin: 0;
  outline: 0px;
  scroll-behavior: smooth;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}

/* preloader- start 
===============================*/

#preloader {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: fixed;
  overflow: visible;
  background: #fff url("../images/preloader.gif") no-repeat center center;
  animation: loader 5s infinite ease;
}

/* preloader - end 
=================================*/
.nav-section {
  background: #333646;
  padding: 10px;
}
.navbar {
  width: 100%;
}
label.logo {
  color: white;
  font-size: 35px;
  line-height: 80px;
  padding: 0 100px;
  font-weight: bold;
}

.navbar ul li {
  display: inline-block;
  line-height: 80px;
  margin: 0 10px;
}
.navbar ul li a {
  color: white;
  font-size: 17px;
  padding: 7px 13px;
  border-radius: 3px;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 00.2px;
}
.navbar a.active,
.navbar a:hover {
  color: #fcb532;
  transition: 0.5s;
}
.checkbtn {
  font-size: 30px;
  color: white;
  float: right;
  line-height: 80px;
  margin-right: 40px;
  cursor: pointer;
  display: none;
}
#check {
  display: none;
}
.drop-down-list {
  visibility: hidden;
  display: flex;
  flex-direction: column;
  color: #fff;
  position: absolute;
  left: 70%;
  z-index: 10000;
  background-color: #333646;
  padding: 10px 100px 10px 30px;
}

#page-dropdown:hover .drop-down-list {
  visibility: visible;
}
/* end nav style */

/* Hero Box Start*/
.contact-hero-box {
  height: 400px;
  background-color: #0a0919;
}
.hero-nav {
  display: flex;
  justify-content: center;
  /* align-items: center; */
  text-align: start;
  flex-direction: column;
  height: 400px;
}
.hero-nav h1 {
  font-size: 60px;
  font-weight: 700;
  color: #fcb532;
}
.hero-nav span {
  color: #ffff;
}
.hero-nav ul {
  height: 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
}
.hero-nav ul li {
  list-style: none;
  margin: 10px;
  color: #ffff;
}
.hero-nav ul li a {
  text-decoration: none;
  color: #ffff;
}
.hero-nav ul li a:hover {
  color: #fcb532;
}
.contact-hero-img {
  height: 400px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.contact-hero-img img {
  height: 300px;
}

/* blog section style */
.contact-section {
  background-color: #252734;
  padding: 100px 0 0;
}

.contact-section h3 {
  font-size: 36pt;
  font-weight: 700;
  text-align: center;
  color: #fff;
}
.contact-form {
  margin-top: 60px;
}
.contact-input-form form {
  height: 500px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 70%;
  margin: 0 auto;
}
.contact-input-form input {
  display: block;
  border: none;
  border-bottom: 0.4px solid rgba(128, 128, 128, 0.685);
  margin-bottom: 20px;
}
.contact-input-form input:focus {
  outline: gray;
}
.contact-input-form textarea {
  height: 100px;
  display: block;
  border: none;
  border-bottom: 0.4px solid rgba(128, 128, 128, 0.685);
}
.contact-input-form textarea:focus {
  outline: gray;
}
.contact-input-form label {
  margin-left: 0;
  color: gray;
}
.contact-input-form button {
  margin-top: 40px;
  border: none;
  background-color: #fcb532;
  width: 30%;
  margin-left: 70%;
  color: #fff;
}
.contact-input-form button i {
  padding: 10px 0;
}
.contact-info {
  height: 500px;
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  /* align-items: center; */
}
.contact-input-form {
  height: 500px;
  background-image: url("../images/contact-bg.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  /* border-radius: 10px; */
}
/* Social Media style */
.social-media-info {
  height: 450px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.social-media-info h5 {
  font-size: 16pt;
  font-weight: 500;
  color: #fcb532;
}
.social-media-info h3 {
  margin-top: 10px;
  font-size: 36pt;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
  text-align: left;
}
.social-media-info p {
  color: #fff;
  font-size: 16px;
}
.social-media-card {
  height: 60px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  text-align: center;
}
.social-media-card img {
  height: 60px;
}
.social-media-follower {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 450px;
}
.follower-info {
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.follower-info h3 {
  font-size: 14pt;
  font-weight: 700;
  margin-bottom: 0;
  color: #0a0919;
}
.follower-info p {
  margin-bottom: 0;
  color: #949494;
}
.plus1 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 0.2px solid rgba(128, 128, 128, 0.301);
  color: #4b5adb;
  padding: 0 20px;
}
/* /* Social Media Style End  */

/* /* Footer Style Start */
.footer-section {
  height: 350px;
}
.footer-nav-section {
  height: 300px;
  background-color: #333646;
}
.footer-nav-container {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.footer-nav-container ul {
  display: flex;
}
.footer-nav-container ul li {
  list-style: none;
  margin: 30px 20px;
}
.footer-nav-container ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.footer-nav-img {
  padding: 30px 0;
}
.footer-nav-container ul li a:hover {
  color: #fcb532;
  transition: 00.2s ease-in;
}
.footer-social-icon {
  margin: 0 0 30px;
}
.footer-social-icon i {
  color: #fff;
  font-size: 30px;
  margin: 0 30px;
}
.footer-social-icon i:hover {
  color: #fcb532;
  transition: 0.3s ease;
}
.footer-nav-card {
  position: relative;
  cursor: pointer;
}
.footer-nav-card img {
  width: 100%;
}
.footer-img-overlay {
  background-color: #fcb532;
  color: #ffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  transition: all 0.5s ease-in;
  position: absolute;
  top: 0;
  bottom: 0;
  transition: all 0.3s ease;
  width: 0;
  text-align: center;
}
.footer-img-overlay h5 {
  margin-top: 20px;
  font-size: 16px;
}
.footer-img-overlay .fab {
  font-size: 26px;
}
.footer-nav-card:hover .footer-img-overlay {
  opacity: 1;
  width: 100%;
}
.footer {
  height: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}
/* /* Footer Style End  */

@media screen and (max-width: 991px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: ease 0.2s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  .navbar a:hover,
  .navbar a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }
  .checkbtn {
    display: block;
  }

  .drop-down-list {
    height: auto;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .footer-section {
    height: auto;
  }
  .footer-nav-section {
    height: auto;
    padding: 30px 0;
  }
  .footer-nav-container {
    height: auto;
  }
  .footer p {
    text-align: center;
  }
}

@media screen and (max-width: 768px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .contact-info {
    margin-top: 45px;
    height: 100px;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
}
  .navbar ul li a {
    font-size: 16px;
  }
  .checkbtn {
    display: block;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .portfolio-hero-box {
    height: auto;
    padding: 30px 0;
    width: 100%;
  }
  .hero-nav {
    height: auto;
    overflow: hidden;
  }
  .contact-section {
    height: auto;
    padding: 50px 0 40px;
  }
  .social-media-info {
    height: auto;
    padding: 50px 0 30px;
  }
  .social-media-info h5 {
    text-align: center;
  }

  .contact-hero-box {
    height: auto;
    padding: 30px 0;
  }
  .contact-hero-img {
    display: none;
  }
  .contact-section {
    height: auto;
    overflow: hidden;
  }
  .footer-section {
    height: auto;
  }
  .footer-nav-section {
    height: auto;
    padding: 40px 0 30px;
  }
  .footer-social-icon {
    margin: 30px 0 30px;
  }
  .footer-nav-container {
    height: auto;
  }
  .footer-nav-container ul {
    display: none;
  }

  .social-media-follower {
    height: 100%;
  }
  .social-media-card {
    width: 100%;
  }
  .plus1 {
    width: 20%;
    padding: 0;
  }

  .follower-info {
    padding: 5px;
  }
  .follower-info h3 {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 0;
    text-align: left;
  }
  .follower-info p {
    font-size: 16px;
    margin-bottom: 0;
  }

  .contact-input-form {
    background-color: #fff;
    border-radius: 5px;
  }
  .contact-input-form {
    height: 600px;
    background-image: none;
    background-color: #fff;
    border-radius: 10px;
  }
}

@media (max-width: 500px) {
  .checkbtn {
    display: block;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .hero-nav h1 {
    font-size: 40px;
  }
  .contact-section {
    height: auto;
    overflow: hidden;
    padding: 30px 0;
  }
  .contact-section h3 {
    font-size: 30px;
  }
  .contact-info {
    display: none;
  }
  .contact-input-form {
    width: 90%;
    margin: 0 auto;
  }
  .contact-info p {
    display: none;
  }
  .social-media-info {
    text-align: center;
  }
  .social-media-info h3 {
    margin-top: 10px;
    font-size: 26pt;
    font-weight: 700;
    color: #fff;
    margin-bottom: 20px;
    text-align: center;
  }
  .global-color{
    color: #fff;
  }
  .contact-info {
    height: auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    padding: 0;
  }
  .contact-section h3 {
    font-size: 26pt;
    font-weight: 700;
    text-align: center;
  }
  .follower-info h3 {
    font-size: 16pt;
  }
  .social-media-info {
    height: auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-top: 30px;
    text-align: center;
  }
  .plus1 {
    width: 20%;
  }
  .footer-nav-card img {
    width: 100%;
    margin: 0;
  }
  .footer-social-icon .fab {
    margin: 10px;
    font-size: 20px;
  }
  .footer-img-overlay {
    text-align: center;
    height: 100%;
  }
  .footer-img-overlay .fab {
    font-size: 36px;
  }
}
