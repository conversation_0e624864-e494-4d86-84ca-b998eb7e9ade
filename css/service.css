/* Google fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Common css -start
=========================== */
html {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

body {
  font-family: "Poppins", sans-serif;
  padding: 0;
  margin: 0;
}

* {
  padding: 0;
  margin: 0;
  outline: 0px;
  scroll-behavior: smooth;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}
/* preloader- start 
===============================*/

#preloader {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: fixed;
  overflow: visible;
  background: #fff url("../images/preloader.gif") no-repeat center center;
  animation: loader 5s infinite ease;
}

/* preloader - end 
=================================*/
/* navbar css
======================== */
.nav-section {
  background: #333646;
  padding: 10px;
}
.navbar {
  width: 100%;
}
label.logo {
  color: white;
  font-size: 35px;
  line-height: 80px;
  padding: 0 100px;
  font-weight: bold;
}

.navbar ul li {
  display: inline-block;
  line-height: 80px;
  margin: 0 10px;
}
.navbar ul li a {
  color: white;
  font-size: 17px;
  padding: 7px 13px;
  border-radius: 3px;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 00.2px;
}
.navbar a.active,
.navbar a:hover {
  color: #fcb532;
  transition: 0.5s;
}
.checkbtn {
  font-size: 30px;
  color: white;
  float: right;
  line-height: 80px;
  margin-right: 40px;
  cursor: pointer;
  display: none;
}
#check {
  display: none;
}

.drop-down-list {
  visibility: hidden;
  display: flex;
  flex-direction: column;
  color: #fff;
  position: absolute;
  left: 70%;
  z-index: 10000;
  background-color: #333646;
  padding: 10px 100px 10px 30px;
}

#page-dropdown:hover .drop-down-list {
  visibility: visible;
}
/* End Nav Style */

/* Hero Box Start*/
.service-hero-box {
  height: 400px;
  background-color: #0a0919;
}

.hero-nav {
  display: flex;
  justify-content: center;
  /* align-items: center; */
  text-align: start;
  flex-direction: column;
  height: 400px;
}
.hero-nav h1 {
  font-size: 60px;
  font-weight: 700;
  color: #fcb532;
}
.hero-nav span {
  color: #ffff;
}
.hero-nav ul {
  height: 30px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
}
.hero-nav ul li {
  list-style: none;
  margin: 10px;
  color: #ffff;
}
.hero-nav ul li a {
  text-decoration: none;
  color: #ffff;
}
.hero-nav ul li a:hover {
  color: #fcb532;
}
.service-hero-img {
  height: 400px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.about-hero-img img {
  height: 300px;
}
/* Hero Box End*/
/* Service Style Start*/
.service-section {
  height: 1050px;
  background-color: #333646;
  text-align: center;
}
.service-title {
  padding-top: 100px;
}
.service-title p {
  font-size: 16pt;
  font-weight: 400;
  color: #fcb532;
}
.service-section h3 {
  font-size: 36pt;
  font-weight: 700;
  color: #ffff;
}
.service-card {
  height: 250px;
  position: relative;
  margin-top: 100px;
}
.service-icon {
  position: absolute;
  top: -15%;
  left: 12%;
  height: 80px;
  width: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #ffff;
}
.service-info {
  border: 1px solid #ffff;
  border-radius: 0 50px 0 0;
  padding: 80px 47px 30px;
  text-align: left;
  color: #ffff;
}
.service-info h5 {
  margin-bottom: 30px;
}
.service-info:hover {
  border: none;
  background-color: #fcb532;
  transition: 0.2s ease-in-out;
}
.service-card:hover .service-icon {
  background-color: #fff;
  transition: 0.2s ease-in-out;
  color: #fcb532;
}
/* Service Style End*/

/* footer */
/* /* Footer Style Start */
.footer-section {
  height: 350px;
}
.footer-nav-section {
  height: 300px;
  background-color: #333646;
}

.footer-nav-container {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.footer-nav-container ul {
  display: flex;
}
.footer-nav-container ul li {
  list-style: none;
  margin: 30px 20px;
}
.footer-nav-container ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}
.footer-nav-container ul li a:hover {
  color: #fcb532;
  transition: 00.2s ease-in;
}
.footer-social-icon {
  margin: 0 0 30px;
}
.footer-social-icon i {
  color: #fff;
  font-size: 30px;
  margin: 0 30px;
}
.footer-social-icon i:hover {
  color: #fcb532;
  transition: 0.3s ease;
}
.footer-nav-card {
  position: relative;
  cursor: pointer;
}
.footer-nav-card img {
  width: 100%;
}
.footer-img-overlay {
  background-color: #fcb532;
  color: #ffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  transition: all 0.5s ease-in;
  position: absolute;
  top: 0;
  bottom: 0;
  transition: all 0.3s ease;
  width: 0;
  text-align: center;
}
.footer-img-overlay h5 {
  margin-top: 20px;
  font-size: 16pt;
}
.footer-img-overlay .fab {
  font-size: 26px;
}
.footer-nav-card:hover .footer-img-overlay {
  opacity: 1;
  width: 100%;
}
.footer {
  height: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}
/* /* Footer Style End  */
@media screen and (max-width: 991px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: ease 0.2s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  .navbar a:hover,
  .navbar a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }
  .checkbtn {
    display: block;
  }

  .drop-down-list {
    height: auto;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .service-info {
    height: auto;
    padding: 70px 10px 20px;
  }
  .footer-section {
    height: auto;
  }
  .footer-nav-section {
    height: auto;
  }
  .footer-nav-container {
    height: auto;
    padding-bottom: 30px;
  }
}

@media screen and (max-width: 768px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }
  .navbar ul li a {
    font-size: 16px;
  }
  .checkbtn {
    display: block;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .hero-nav {
    height: auto;
    padding: 30px 0;
  }

  .service-hero-box {
    height: auto;
  }
  .service-section {
    height: auto;
    overflow: hidden;
    padding: 50px 0;
  }
  .service-section h3 {
    font-size: 40px;
    font-weight: 700;
    color: #ffff;
  }
  .service-hero-img {
    display: none;
  }

  .service-card {
    height: 300px;
    position: relative;
    margin-top: 100px;
  }
  .service-info {
    height: 300px;
  }
  .hero-nav h1 {
    font-size: 40px;
  }

  .footer-section {
    height: auto;
    padding: 30px 0 0;
    background-color: #333646;
  }
  .footer-nav-container ul {
    display: none;
  }
  .footer-social-icon {
    margin: 20px 0;
  }
  .footer-nav-section {
    height: auto;
    padding: 0;
  }
  .footer-nav-container {
    height: auto;
  }
  .footer p {
    text-align: center;
    font-size: 16px;
  }
}

@media (max-width: 526px) {
  .checkbtn {
    display: block;
  }
  .service-hero-img {
    display: none;
  }
  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: all 0.5s;
    z-index: 1000000;
    padding-left: 0;
  }
  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }
  nav ul li a {
    font-size: 20px;
  }
  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }
  #check:checked ~ ul {
    left: -12%;
  }

  .drop-down-list {
    height: 0;
    /* transition: 0.5s all; */
  }
  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }
  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .service-hero-box {
    height: auto;
  }
  .service-section {
    height: auto;
    position: static;
    padding-bottom: 50px;
    overflow: hidden;
  }
  .service-title {
    padding: 0;
  }
  .service-title h3 {
    font-size: 30px;
  }
  .footer-section {
    height: auto;
    overflow: hidden;
  }
  .footer-nav-container {
    height: auto;
  }
  .footer-nav-section {
    height: auto;
  }
  .footer-social-icon i {
    color: #fff;
    font-size: 24px;
    margin: 20px 10px 0;
  }
  .footer-nav-container ul {
    display: none;
  }
  .footer {
    height: auto;
  }
  .footer-img-overlay {
    text-align: center;
  }
  .footer-img-overlay .fab {
    font-size: 20px;
  }
  .footer-img-overlay h5 {
    font-size: 12px;
  }
}
