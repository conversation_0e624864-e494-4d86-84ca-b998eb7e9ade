/* Google fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
/*=========table of content start

  Template Name: <PERSON> (Web Developer)
  Template URL: https://theme.bitspecksolutions.com/html-template/black-denny/
  Author: Bitspeck Solutions

===================================

CSS 

=================================

Preloader

===================================

/// Pages 

1. Index Page.
  - Header.
  - Hero section.
  - Features.
  - About.
  - Service.
  - Skill & Experiance.
  - Portfolio.
  - Project.
  - Blog.
  - Follow.
2. About Page.
3. Service Page.
4. Portfolio Page.
5. Blog Page.
6. Contact Page.

=================================

Index Page

==================================

==============table of content end */

/* Common css -start
=========================== */
html {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

body {
  font-family: "Poppins", sans-serif;
  padding: 0;
  margin: 0;
}

* {
  padding: 0;
  margin: 0;
  outline: 0px;
  scroll-behavior: smooth;
  text-decoration: none;
  list-style: none;
  box-sizing: border-box;
}

/* preloader- start 
===============================*/

#preloader {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: fixed;
  overflow: visible;
  background: #fff url("../images/preloader.gif") no-repeat center center;
  animation: loader 5s infinite ease;
}

/* preloader - end 
=================================*/

.header {
  width: 100%;
}

.global-color {
  color: #fcb532;
}

/* Start Nav Style */

.nav-section {
  background: #333646;
  padding: 10px;
}

.navbar {
  width: 100%;
}

label.logo {
  color: white;
  font-size: 35px;
  line-height: 80px;
  padding: 0 100px;
  font-weight: bold;
}

.navbar ul li {
  display: inline-block;
  line-height: 56px;
  margin: 0 10px;
}

.navbar ul li a {
  color: white;
  font-size: 17px;
  padding: 7px 13px;
  border-radius: 3px;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 00.2px;
}

.navbar a.active,
.navbar a:hover {
  color: #fcb532;
  transition: 0.5s;
}

.checkbtn {
  font-size: 30px;
  color: white;
  float: right;
  line-height: 80px;
  margin-right: 40px;
  cursor: pointer;
  display: none;
}

#check {
  display: none;
}

.drop-down-list {
  visibility: hidden;
  display: flex;
  flex-direction: column;
  color: #fff;
  position: absolute;
  left: 72%;
  z-index: 10000;
  background-color: #333646;
  padding: 10px 25px 10px 10px;
}

#page-dropdown:hover .drop-down-list {
  visibility: visible;
}

/* End Nav Style */

/* Start Hero box Style */
.hero-section {
  height: 700px;
  overflow: hidden;
  background-image: url(../images/heroBg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
}

.hero-info {
  height: 700px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.hero-info h3 {
  font-size: 26pt;
  font-weight: 500;
  padding: 0;
}

.hero-info h1 {
  font-size: 61pt;
  font-weight: 700;
}

.hero-info p {
  font-size: 16pt;
  font-family: "Open Sans", sans-serif;
  font-weight: 400;
  margin: 50px 0;
  padding: 0;
}

/* End Hero box Style */

/* Feature Start */
.features-section {
  height: 220px;
  background-color: #333646;
  position: relative;
  bottom: 110px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 3;
}

.features-card {
  display: flex;
  color: #ffff;
}

.features-card h3 {
  font-size: 18pt;
  font-weight: 700;
}

.features-icon p {
  font-size: 16pt;
  font-weight: 400;
}

.features-icon {
  color: #fcb532;
  font-size: 40px;
  padding-right: 25px;
}

/* Feature End */

/* About style start */
.about-section {
  height: 900px;
  background-color: #252734;
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  margin-top: -220px;
  text-align: center;
  overflow: hidden;
}

.about-img {
  height: 900px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-details {
  height: 900px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: left;
}

.about-details h3 {
  color: #fcb532;
  font-size: 18pt;
  font-weight: 500;
}

.about-details h1 {
  color: #ffff;
  font-size: 36pt;
  font-weight: 700;
}

.about-details p {
  color: #ffff;
  font-size: 16px;
  font-weight: 400;
  font-family: "Open Sans", sans-serif;
}

.about-contact-info {
  display: flex;
  margin-top: 60px;
}

.about-contact-info img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}

.about-contact-info h3 {
  font-size: 18pt;
  color: #ffff;
}

.about-contact-info .fab {
  font-size: 20px;
  color: #ffff;
  padding: 10px;
  border: 1px solid #ffff;
  border-radius: 50%;
  transition: 0.2s ease-in-out;
  margin-right: 10px;
}

.about-contact-info .fa-facebook-f {
  padding: 10px 13px;
}

.about-contact-info .fab:hover {
  color: #fcb532;
  border: 1px solid #fcb532;
}

/* About style end */

/* Service Style Start*/
.service-section {
  position: relative;
  top: 670px;
  height: auto;
  background-color: #333646;
  text-align: center;
  padding-bottom: 150px;
}

.service-title {
  padding-top: 100px;
}

.service-title p {
  font-size: 16pt;
  font-weight: 400;
  color: #fcb532;
}

.service-title h3 {
  font-size: 36pt;
  font-weight: 700;
  color: #ffff;
}

.service-card {
  height: 250px;
  position: relative;
  margin-top: 100px;
}

.service-icon {
  position: absolute;
  top: -15%;
  left: 12%;
  height: 80px;
  width: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #ffff;
}

.service-info {
  border: 1px solid #ffff;
  border-radius: 0 50px 0 0;
  flex-direction: column;
  align-items: flex-start;
  padding: 71px 10px 40px 47px;
  text-align: left;
  color: #ffff;
}

.service-info h5 {
  margin-bottom: 30px;
}

.service-info p {
  text-align: left;
}

.service-info:hover {
  border: none;
  background-color: #fcb532;
  transition: 0.2s ease-in-out;
}

.service-card:hover .service-icon {
  background-color: #fff;
  transition: 0.2s ease-in-out;
  color: #fcb532;
}

/* Service Style End*/
/* Skill Style Start*/
.skill-section {
  position: relative;
  top: 680px;
  height: auto;
  background-color: #252734;
  padding-bottom: 150px;
}

.skill-img {
  height: 680px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.skill-title {
  height: 680px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.skill-title h5 {
  font-size: 16pt;
  font-weight: 600;
  color: #fcb532;
}

.skill-title h3 {
  font-size: 36pt;
  font-weight: 700;
  color: #ffff;
}

/* Skill Style End*/

/* Experience style start */
.experience-section {
  height: auto;
  margin: 100px 0;
}

.experience-section h5 {
  color: #fcb532;
  font-size: 16pt;
  font-weight: 400;
  text-align: center;
}

.experience-section h3 {
  color: #ffff;
  font-size: 36pt;
  font-weight: 700;
  text-align: center;
}

.experience-details {
  margin-top: 75px;
}

.experience-container {
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* align-items: center; */
}

.experience-card {
  display: flex;
  margin-bottom: 30px;
}

.experience-card img {
  width: 30px;
  height: 100px;
}

.experience-info {
  margin-left: 60px;
  text-align: left;
}

.experience-card h5 {
  font-size: 24px;
  color: #fff;
  font-weight: 500;
  text-align: left;
}

.experience-card .company-name {
  color: #fff;
  margin-bottom: 20px;
}

.experience-img {
  width: 100%;
  height: 100%;
}

/* Experience style end */
/* Fortfolio style Start */
.portfolio-section {
  background-color: #333646;
  position: relative;
  top: 580px;
  height: 800px;
  color: #fff;
  font-size: 30px;
}

.portfolio-section h5 {
  font-size: 16pt;
  color: #fcb532;
  padding: 100px 10px 0;
  text-align: center;
}

.portfolio-section h3 {
  font-size: 36pt;
  color: #fff;
  font-weight: 500;
  text-align: center;
}

.portfolio-container {
  width: 80%;
  margin: 0 auto;
  height: auto;
  padding: 20px 0;
  display: flex;
  overflow: hidden;
}

.portfolio-container ul {
  display: flex;
}

.portfolio-slider {
  margin-top: 50px;
}

.portfolio-slider h4 {
  color: #fff;
}

.swiper-container {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
  overflow: hidden;
}

.swiper-slide {
  background-position: center;
  background-size: cover;
  width: 300px;
  height: 300px;
  border-radius: 10px;
}

.swiper-slide-active {
  position: relative;
  bottom: 60px;
  height: 350px;
  width: 350px;
  margin: 0 30px;
  border-radius: 20px;
}

.slider-nav {
  height: 100px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.slider-nav ul {
  display: flex;
  justify-self: center;
  align-items: center;
  padding: 0;
}

.slider-nav ul li {
  list-style: none;
  margin: 0 10px;
  font-size: 16px;
}

.slider-nav ul li a {
  text-decoration: none;
  color: #ffff;
  font-size: 15px;
}

.slider-nav ul li a:hover {
  color: #fcb532;
  border-bottom: 5px solid #fcb532;
  padding-bottom: 20px;
}
.slider-nav ul li a.clicked {
  border-bottom: 5px solid #fcb532;
  padding-bottom: 20px;
}

.slider-nav hr {
  width: 40%;
  background-color: #fff;
  height: 5px;
  margin: 0 auto;
}

/* Fortfolio style end */
/* Project style Start */
.project-section {
  background-color: #252734;
  height: 420px;
  position: relative;
  top: 565px;
  overflow: hidden;
}

.project-info {

  height: 420px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.project-info h3 {
  font-size: 30px;
  margin-bottom: 30px;
  font-weight: 700;
  color: #fff;
}

.project-info p {
  font-size: 16px;
  color: #fff;
}

.project-info button {
  width: 180px;
  height: 50px;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 1px;
  border: 1px solid #fcb532;
  border-radius: 5px;
  background-color: #252734;
  color: #fcb532;
}

.project-info button:hover {
  background-color: #fcb532;
  color: #fff;
  transition: 0.2s ease-in;
}

/* Project style End */
/* Newws style Start */
.news-section {
  height: 900px;
  position: relative;
  top: 550px;
  background-color: #333646;
}

.news-section h3 {
  font-size: 36pt;
  font-weight: 700;
  color: #fff;
  text-align: center;
  padding-bottom: 70px;
}

.news-section h5 {
  font-size: 16pt;
  font-weight: 400;
  color: #fcb532;
  text-align: center;
  padding-top: 100px;
}

.news-container hr {
  width: 100%;
  height: 2px;
  background-color: #fcb532;
}

.card {
  background-color: #252734 !important;
}

.card h4 {
  font-size: 20px;
  font-weight: 400;
  color: #fff;
}

.card p {
  color: #fff;
  margin-top: 20px;
  font-weight: 200;
  font-size: 15px;
}

.card .read-btn {
  font-size: 16px;
  color: #fcb532;
  background-color: #252734;
  border: none;
}

.card-likes {
  display: flex;
}

.card-likes p {
  margin-right: 40px;
  margin-left: 20px;
  margin-top: 0;
}

/* Newws style End */

/* Social Media Style Start */
.social-media-section {
  background-color: #252734;
  height: 550px;
  position: relative;
  top: 550px;
}

.social-media-info {
  height: 550px;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.social-media-info h5 {
  font-size: 16pt;
  font-weight: 500;
  color: #fcb532;
}

.social-media-info h3 {
  margin-top: 10px;
  font-size: 36pt;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
}

.social-media-info p {
  color: #fff;
  font-size: 16px;
}

.social-media-card {
  height: 60px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
}

.social-media-card img {
  height: 60px;
}

.social-media-follower {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 550px;
}

.follower-info {
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.follower-info h3 {
  font-size: 14pt;
  font-weight: 700;
  margin-bottom: 0;
}

.follower-info p {
  margin-bottom: 0;
  color: #949494;
}

.plus1 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 0.2px solid rgba(128, 128, 128, 0.301);
  color: #4b5adb;
  padding: 0 20px;
}

/* /* Social Media Style End  */
/* /* Footer Style Start */
.footer-section {
  height: 350px;
  position: relative;
  top: 550px;
}

.footer-nav-section {
  height: 300px;
  background-color: #333646;
}

.footer-nav-container {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.footer-nav-container ul {
  display: flex;
}

.footer-nav-container ul li {
  list-style: none;
  margin: 30px 20px;
}

.footer-nav-container ul li a {
  text-decoration: none;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer-nav-container ul li a:hover {
  color: #fcb532;
  transition: 00.2s ease-in;
}

.footer-social-icon {
  margin: 0 0 30px;
}

.footer-social-icon i {
  color: #fff;
  font-size: 30px;
  margin: 0 30px;
}

.footer-social-icon i:hover {
  color: #fcb532;
  transition: 0.3s ease;
}

.footer-nav-card {
  position: relative;
  cursor: pointer;
}

.footer-nav-card img {
  width: 100%;
}

.footer-img-overlay {
  background-color: #fcb532;
  color: #ffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  transition: all 0.5s ease-in;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transition: all 0.3s ease;
  width: 0;
  text-align: center;
}

.footer-img-overlay h5 {
  margin-top: 20px;
  font-size: 16px;
}

.footer-img-overlay .fab {
  font-size: 26px;
}

.footer-nav-card:hover .footer-img-overlay {
  opacity: 1;
  width: 100%;
  position: absolute;
}

.footer {
  height: 80px;
  background-color: #fcb532;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #fff;
  font-weight: 400;
}

/* /* Footer Style End  */

/* Media Query */
@media screen and (max-width: 965px) {
  .nav-link-container {
    display: none;
  }

  .logo-img img {
    width: 50%;
  }

  .card {
    margin-top: 20px;
  }

  .nav-manu {
    width: 100%;
  }

  .service-section {
    height: 1200px;
  }

  .service-card {
    height: 300px;
    position: relative;
    margin-top: 100px;
  }

  .service-info {
    height: 300px;
  }

  .social-media-follower {
    height: 100%;
  }

  .social-media-card {
    width: 100%;
  }

  .plus1 {
    width: 100%;
    padding: 0;
  }

  .follower-info {
    padding: 5px;
  }

  .follower-info h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 0;
    text-align: left;
  }

  .follower-info p {
    font-size: 10px;
  }

  .checkbtn {
    display: block;
  }

  .footer-nav-card:hover .footer-img-overlay {
    opacity: 1;
    width: 100%;
    position: absolute;
    left: 0;
  }
}

@media screen and (max-width: 991px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }

  .navbar ul li a {
    font-size: 16px;
  }

  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: ease 0.2s;
    z-index: 1000000;
    padding-left: 0;
  }

  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }

  nav ul li a {
    font-size: 20px;
  }

  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }

  #check:checked~ul {
    left: -12%;
  }

  .drop-down-list {
    height: auto;
    /* transition: 0.5s all; */
  }

  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }

  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .hero-section {
    height: auto;
    padding: 50px 10px;
    overflow: hidden;
    background-image: url(../images/heroBg.jpg);
    /* background-position-x: -750px; */
  }

  .hero-info h1 {
    font-size: 65px;
  }

  .hero-info p {
    font-size: 16pt;
    margin: 30px 0;
    padding: 0;
  }

  .features-section {
    display: none;
  }

  .about-section {
    height: auto;
    position: static;
    padding-bottom: 50px;
    padding-top: 50px;
    margin-top: 0px;
    overflow: hidden;
  }

  .about-section .about-img {
    height: auto;
    padding-top: 50px;
  }

  .about-section .about-details {
    height: auto;
    display: flex;
  }

  .about-info-container {
    height: auto;
  }

  .about-contact-info {
    display: block;
    flex-direction: column;
  }

  .about-contact-info img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
    margin-bottom: 20px;
  }

  .hero-info {
    height: auto;
  }

  .service-section {
    height: auto;
    position: static;
    padding-bottom: 70px;
    overflow: hidden;
  }

  .service-title {
    padding: 50px 0;
  }

  .service-title h3 {
    font-size: 26pt;
  }

  .service-info h5 {
    margin-bottom: 20px;
    margin-top: 20px;
  }

  .service-info {
    height: auto;
    border: 1px solid #ffff;
    border-radius: 0 50px 0 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 55px 30px 10px;
  }

  .skill-section {
    height: auto;
    position: static;
    overflow: hidden;
    padding: 50px;
  }

  .skill-img img {
    width: 100%;
  }

  .skill-title {
    text-align: center;
  }

  .skill-title h3 {
    font-size: 30px;
  }

  .experience-section {
    margin-top: 50px;
    padding-bottom: 50px;
    margin-bottom: 0px;
    overflow: hidden;
  }

  .experience-img {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .portfolio-section {
    height: auto;
    position: static;
  }

  .project-section {
    height: auto;
    position: static;
    overflow: hidden;
  }

  .project-img {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }

  .project-img img {
    width: 100%;
  }

  .social-media-section {
    padding: 40px 0;
  }

  .social-media-info {
    height: 100%;
    display: flex;
    justify-content: center;
  }

  .social-media-card {
    width: 100%;
  }

  .follower-info h3 {
    font-size: 14px;
    padding: 0 5px;
  }

  .follower-info p {
    font-size: 12px;
    padding: 0 5px;
  }

  .follower-info {
    padding: 0;
  }

  .plus1 {
    width: 20%;
    padding: 5px;
  }

  .slider-nav hr {
    width: 80%;
  }

  .news-section {
    height: auto;
    position: static;
    overflow: hidden;
    padding-bottom: 50px;
  }

  .social-media-section {
    height: auto;
    overflow: hidden;
    position: static;
  }

  .footer-section {
    height: auto;
    position: static;
    overflow: hidden;
  }

  .footer-nav-section {
    height: auto;
    padding-bottom: 40px;
  }

  .footer-nav-container {
    height: auto;
    padding-top: 40px;
  }

  .footer-nav-container ul {
    display: none;
  }

  .footer-social-icon {
    text-align: center;
    margin-top: 20px;
  }

  .footer-img-overlay {
    text-align: center;
  }

  .footer-img-overlay h5 {
    margin-top: 20px;
    font-size: 16px;
  }

  .footer {
    height: auto;
    text-align: center;
  }

  .checkbtn {
    display: block;
  }

  .slider-nav {
    height: 49px;
    margin: 20px auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fcb532;
  }

  .slider-nav hr {
    display: none;
  }

  .slider-nav ul {
    margin-top: 12px;
  }

  .slider-nav ul li {
    list-style: none;
    font-size: 16px;
    margin: 0;
    border: none;
  }

  .slider-nav ul li a {
    padding: 15px 10px;
    margin: 0;
    border: none;
  }

  .slider-nav ul li a:hover {
    background-color: #c88d1e;
    color: #fff;
    padding: 17px 10px;
    margin: 0;
    border: none;
  }
}

@media screen and (max-width: 768px) {
  label.logo {
    font-size: 30px;
    padding-left: 50px;
  }

  .navbar ul li a {
    font-size: 16px;
  }

  .navbar ul {
    position: absolute;
    width: 125%;
    height: auto;
    padding: 10px 0;
    background: #333646;
    top: 125px;
    right: 0%;
    left: -150%;
    text-align: center;
    transition: ease 0.2s;
    z-index: 1000000;
    padding-left: 0;
  }

  .navbar ul li {
    display: block;
    margin: 30px 0;
    line-height: 30px;
  }

  nav ul li a {
    font-size: 20px;
  }

  a:hover,
  a.active {
    background: none;
    color: #fcb532;
  }

  #check:checked~ul {
    left: -12%;
  }

  .drop-down-list {
    height: auto;
    /* transition: 0.5s all; */
  }

  #page-dropdown:hover .drop-down-list {
    visibility: visible;
    position: static;
    background-color: #2c2f3d;
    height: 100%;
    width: 100%;
  }

  #page-dropdown ul li {
    padding: 5px;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .hero-section {
    height: auto;
    padding: 50px 10px;
    overflow: hidden;
    background-image: url(../images/heroBg.jpg);
    /* background-position-x: -750px; */
  }

  .hero-info h1 {
    font-size: 65px;
  }

  .hero-info p {
    font-size: 16pt;
    margin: 30px 0;
    padding: 0;
  }

  .features-section {
    display: none;
  }

  .about-section {
    height: auto;
    position: static;
    padding-bottom: 50px;
    padding-top: 50px;
    margin-top: 0px;
    overflow: hidden;
  }

  .about-section .about-img {
    height: auto;
    padding-top: 50px;
  }

  .about-section .about-details {
    height: auto;
    display: flex;
  }

  .about-info-container {
    height: auto;
  }

  .about-contact-info {
    display: block;
    flex-direction: column;
  }

  .about-contact-info img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
    margin-bottom: 20px;
  }

  .hero-info {
    height: auto;
  }

  .service-section {
    height: auto;
    position: static;
    padding-bottom: 50px;
    overflow: hidden;
  }

  .service-title {
    padding: 50px 0 0;
  }

  .service-title h3 {
    font-size: 26pt;
  }

  .service-info h5 {
    margin-bottom: 20px;
    margin-top: 20px;
  }

  .skill-section {
    height: auto;
    position: static;
    overflow: hidden;
  }

  .skill-img img {
    width: 100%;
  }

  .skill-title {
    text-align: center;
  }

  .skill-title h3 {
    font-size: 30px;
  }

  .experience-section {
    margin-top: 50px;
    padding-bottom: 50px;
    margin-bottom: 0px;
    overflow: hidden;
  }

  .experience-img {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .portfolio-section {
    height: auto;
    position: static;
  }

  .project-section {
    height: auto;
    position: static;
    overflow: hidden;
  }

  .project-img {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
  }

  .project-img img {
    width: 100%;
  }

  .slider-nav hr {
    width: 80%;
  }

  .news-section {
    height: auto;
    position: static;
    overflow: hidden;
    padding-bottom: 50px;
  }

  .news-section h5 {
    font-size: 16pt;
    font-weight: 400;
    color: #fcb532;
    text-align: center;
    padding-top: 100px;
    padding: 50px 0 0;
  }

  .social-media-section {
    height: auto;
    overflow: hidden;
    position: static;
    padding-bottom: 50px;
    padding-top: 0;
  }

  .social-media-info h3 {
    margin-top: 10px;
    font-size: 30pt;
    font-weight: 700;
  }

  .social-media-info {
    height: auto;
    padding: 0 0 30px;
    text-align: center;
    margin-top: 50px;
  }

  .follower-info h3 {
    font-size: 20px;
  }

  .follower-info p {
    font-size: 16px;
  }

  .plus1 {
    width: 20%;
  }

  .footer-section {
    height: auto;
    position: static;
    overflow: hidden;
  }

  .footer-nav-section {
    height: auto;
    padding-bottom: 40px;
  }

  .project-info {
    height: auto;
    padding: 50px;
    text-align: center;
  }

  .project-info button {
    margin: 0 auto;
  }

  .footer-nav-container {
    height: auto;
    padding-top: 40px;
  }

  .footer-nav-container ul {
    display: none;
  }

  .footer-social-icon {
    text-align: center;
    margin-top: 20px;
  }

  .footer-img-overlay {
    text-align: center;
  }

  .footer-img-overlay h5 {
    margin-top: 20px;
    font-size: 16px;
  }

  .footer {
    height: auto;
    text-align: center;
  }

  .checkbtn {
    display: block;
  }

  .slider-nav {
    height: 49px;
    margin: 20px auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fcb532;
  }

  .slider-nav hr {
    display: none;
  }

  .slider-nav ul {
    margin-top: 12px;
  }

  .slider-nav ul li {
    list-style: none;
    font-size: 16px;
    margin: 0;
    border: none;
  }

  .slider-nav ul li a {
    padding: 15px 10px;
    margin: 0;
    border: none;
  }

  .slider-nav ul li a:hover {
    background-color: #c88d1e;
    color: #fff;
    padding: 17px 10px;
    margin: 0;
    border: none;
  }
}

@media screen and (max-width: 526px) {
  .hero-section {
    height: auto;
    padding: 50px 10px;
    overflow: hidden;
    background-image: none;
    background-color: #0a0919;
    background-position-x: -650px;
    text-align: center;
  }

  .hero-info h1 {
    font-size: 45px;
  }

  .hero-info p {
    font-size: 16pt;
    font-weight: 400;
    margin: 20px 0;
    padding: 0;
  }

  .about-details {
    margin-top: 20px;
    text-align: center;
  }

  .about-contact-info {
    text-align: center;
  }

  .about-contact-info img {
    margin: 0 35% 20px;
  }

  .about-details h1 {
    color: #ffff;
    font-size: 30px;
    font-weight: 700;
  }

  .about-social {
    width: 90%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
  }

  .service-card {
    margin-top: 70px;
    height: 100%;
  }

  .service-title h3 {
    font-size: 30px;
  }

  .service-info h5 {
    margin-top: 40px;
  }

  .service-title {
    padding: 30px 0 0;
    font-size: 16pt;
  }

  .skill-section {
    padding: 30px 0;
  }

  .skill-img {
    height: auto;
  }

  .skill-title {
    height: auto;
  }

  .portfolio-section h5 {
    font-size: 16pt;
    padding: 50px 10px 0;
  }

  .project-info {
    height: auto;
    padding: 30px 5px 40px;
    text-align: center;
  }

  .portfolio-section h3 {
    font-size: 26pt;
  }

  .experience-section h3 {
    font-size: 30px;
  }

  .portfolio-section h3 {
    font-size: 30px;
    margin-bottom: 30px;
  }

  .slider-nav {
    height: 49px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fcb532;
  }

  .slider-nav hr {
    display: none;
  }

  .slider-nav ul {
    margin-top: 12px;
  }

  .slider-nav ul li {
    list-style: none;
    font-size: 16px;
    margin: 0;
    border: none;
  }

  .slider-nav ul li a {
    padding: 15px 10px;
    margin: 0;
    border: none;
  }

  .slider-nav ul li a:hover {
    background-color: #e69f1d;
    color: #fff;
    padding: 16px 10px 18px;
    margin: 0;
    border: none;
  }

  .experience-info {
    text-align: left;
  }

  .experience-info p {
    text-align: left;
  }

  .news-section h3 {
    font-size: 26pt;
  }

  .social-media-info {
    text-align: center;
  }

  .social-media-info h3 {
    margin-top: 10px;
    font-size: 26pt;
    font-weight: 700;
    color: #fff;
    margin-bottom: 20px;
  }

  .social-media-container {
    padding: 30px 0;
  }

  .plus1 {
    width: 20%;
    padding: 0;
  }

  .project-info h3 {
    text-align: center;
    font-size: 30px;
  }

  .project-info p {
    text-align: center;
  }

  .project-info button {
    margin: 0 auto;
  }

  .footer-img-overlay .fab {
    font-size: 36px;
  }

  .footer-img-overlay h5 {
    font-size: 12px;
  }

  .footer-social-icon .fab {
    font-size: 25px;
    margin: 10px;
  }
}


/*========================================

 ========index page - start

=========================================*/

.indexlink {
  background: #f26522;
  color: #fff !important;
}

.image-container span img {
  height: auto;
  max-width: 50%;
}

.features-item {
  background-color: #f1f1f1;
  padding: 30px;
}

.features-item .title-text {
  font-size: 24px;
  margin-top: 15px;
  margin-bottom: 0px;
  color: #000000;
}

#preview-section {
  padding: 100px 0px;
}

#preview-section .section-tittle::before {
  content: '';
  position: absolute;
  width: 80px;
  height: 2px;
  background-color: #f26522;
}

.preview-item {
  position: relative;
}

.overlay-preview {
  position: absolute;
  left: 0px;
  right: 0px;
  bottom: 0px;
  top: 0px;
  background-color: rgb(15 2 2 / 70%);
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
}

.overlay-preview:hover {
  background-color: rgb(164 108 5 / 50%);
  cursor: pointer;
}

.overlay-preview .title-text {
  color: #fff !important;
  align-items: center;
  font-size: 28px;
  transform: translateY(180px);
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
}

.overlay-preview:hover .title-text {
  font-size: 31px;
  font-weight: 700;
}

.banner-content-preview {
  transform: translateY(500px);
}

.preview-h1 {
  color: #000;
  background: none !important;
  font-family: 'Oswald';
}

.preview-item-img {
  height: 70vh;
}

.preview-item {
  width: 100%;
}

.preview-desc p {
  color: #000;
}

.preview-button {
  background-color: #ffffff;
  padding: 10px 30px;
  display: inline-block;
  border-radius: 50px;
}

.preview-button a {
  color: #cd9962;
}

.preview-button a i {
  margin-left: 5px;
  color: #0e4d8e;
}

#preview-banner-section {
  background-color: #0e4d8e;
}

.preview-menu-sticky {
  background: #ffffff;
  border-bottom: 1px solid #0e4d8e;
}

.indexmenusticky {
  border-bottom: 1px solid #f1f1f11c;
  color: var(--bs-light);
  background: #fff;
  ;
}

.preview-item .image-container img {
  width: 100%;
  height: auto;
}

.register_section {
  padding: 100px;
  background-image: linear-gradient(rgb(252 181 50), rgb(0 0 0));
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  background-position: center;
}

.register_content h2 {
  color: #ffffff;
  font-size: 48px;
  margin-bottom: 30px;
  font-family: "Satisfy", cursive;
}

.register_content h6 {
  font-size: 41px;
  font-weight: 700;
  line-height: 48px;
  color: #efefef;
}

.indexmenusticky .navbar-collapse {
  justify-content: center;
}

.indexmenusticky .navbar-nav .nav-item .nav-link {
  padding: 10px 30px;
  color: #000;
  letter-spacing: 1px;
  font-size: 21px;
}

.indexmenu-header {
  padding: 0px 0px;
}

.stuck .indexmenusticky .navbar {
  background-color: #ccf381;
}

.preview-item-img {
  background: rgb(252, 181, 50);
  background-position: center;
  background-size: cover;
  justify-content: center;
  display: flex;
  align-items: center;
}

.indexmenu-sticky-header {
  padding: 0px;
}

.indexmenu-sticky-header:before {
  background: rgb(0 83 63);
}

@media (min-width: 992px) and (max-width: 1199px) {

  .header .nav-section ul,
  .header .nav-section ol {
    padding-left: 1rem;
  }
}